<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('document_series', function (Blueprint $table) {
            $table->integer('initial_number')->default(1)->after('switch');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('document_series', function (Blueprint $table) {
            $table->dropColumn('initial_number');
        });
    }
};
