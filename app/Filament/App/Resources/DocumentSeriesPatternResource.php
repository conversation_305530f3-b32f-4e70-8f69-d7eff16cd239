<?php

namespace App\Filament\App\Resources;

use App\Enums\DocumentPatternResetSwitch;
use App\Enums\SystemModules;
use App\Filament\App\Resources\DocumentSeriesPatternResource\Pages;
use App\Models\DocumentSeriesPattern;
use App\Repositories\DocumentSeriesRepository;
use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class DocumentSeriesPatternResource extends Resource
{
    protected static ?string $model = DocumentSeriesPattern::class;

    protected static ?string $navigationIcon = 'heroicon-s-hashtag';

    public static function getNavigationLabel(): string
    {
        return 'Numeracja dokumentów';
    }

    public static function getBreadcrumb(): string
    {
        return 'Wzory numeracji dokumentów';
    }

    public static function getNavigationGroup(): ?string
    {
        return __('app._.settings');
    }

    public static function canAccess(): bool
    {
        return (auth()->user()?->isTenantAdmin() ?? false) && (tenant()?->hasModule(SystemModules::INVOICES) ?? false);
    }

    public static function shouldRegisterNavigation(): bool
    {
        return (auth()->user()?->isTenantAdmin() ?? false) && (tenant()?->hasModule(SystemModules::INVOICES) ?? false);
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema(
                [
                    Forms\Components\Section::make(__('app.doc_series_pattern.edit.pattern_section_title'))
                        ->columnSpan(2)
                        ->schema([
                            Forms\Components\Select::make('doc_type')
                                ->required()
                                ->disabled(fn(?Model $record) => $record?->is_default ?? false)
                                ->options(
                                    DocumentSeriesRepository::getSeriesForTenant(tenant())
                                    ->filter(fn(DocumentSeriesPattern $dsp) => $dsp->is_blocked === false)
                                    ->map(
                                        // phpcs:ignore
                                        fn(DocumentSeriesPattern $dsp) => $dsp->setAttribute('label', $dsp->doc_type->label())
                                    )
                                    ->pluck('label', 'doc_type.value')
                                )
                                ->label(__('app.doc_series_pattern.edit.doc_type')),
                            Forms\Components\TextInput::make('name')
                                ->required()
                                ->label(__('app.doc_series_pattern.edit.name')),
                            Forms\Components\Group::make([
                                Forms\Components\TextInput::make('pattern')
                                    ->required()
                                    ->columnSpan(2)
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(
                                        function (
                                            \Livewire\Component $livewire,
                                            Forms\Components\Component $component,
                                            Forms\Set $set,
                                            string $state
                                        ) {
                                            $livewire->validateOnly($component->getStatePath());
                                            $set(
                                                'testp',
                                                DocumentSeriesPattern::generateFullNumberStatic(
                                                    $state,
                                                    random_int(1, 5),
                                                    now()
                                                )
                                            );
                                        }
                                    )
                                    ->rules([
                                        fn(): \Closure => static function (string $attribute, $value, \Closure $fail) {
                                            if (false === DocumentSeriesRepository::checkSeriesPattern($value)) {
                                                $fail(DocumentSeriesRepository::$validation_error);
                                            }
                                            return true;
                                        }
                                    ])
                                    ->helperText(__('app.doc_series_pattern.edit.pattern_hint'))
                                    ->label(__('app.doc_series_pattern.edit.pattern')),
                                Forms\Components\Select::make('switch')
                                    ->required()
                                    ->options(DocumentPatternResetSwitch::class)
                                    ->label(__('app.doc_series_pattern.edit.switch')),
                                Forms\Components\TextInput::make('initial_number')
                                    ->required()
                                    ->default(1)
                                    ->helperText('Numer od którego zacznie się numeracja')
                                    ->label(__('app.doc_series_pattern.edit.initial_number')),
                                TextInput::make('testp')
                                    ->formatStateUsing(fn($operation, Forms\Get $get) => $operation === 'create' ?
                                        '---' :
                                        DocumentSeriesPattern::generateFullNumberStatic(
                                            $get('pattern'),
                                            random_int(1, 5),
                                            now()
                                        ))
                                    ->readOnly()
                                    ->label(__('app.doc_series_pattern.edit.testp'))
                                    ->dehydrated(false)
                            ])->columns(5),
                            Forms\Components\Checkbox::make('is_active')
                                ->disabled(fn(?Model $record) => $record?->is_default ?? false)
                                ->label(__('app.doc_series_pattern.edit.is_active')),
                            Forms\Components\Checkbox::make('is_default')
                                ->disabled()
                                ->label(__('app.doc_series_pattern.edit.is_default')),
                        ]),

                    Forms\Components\Section::make(__('app.doc_series_pattern.edit.legend_section_title'))
                        ->schema([
                            Forms\Components\ViewField::make('opis')
                                ->dehydrated(false)
                                ->view('filament.app.forms.fields.docpatternlegend')
                        ])
                        ->columnSpan(1)
                ]
            )
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('doc_type')
                    ->formatStateUsing(fn($state) => $state->label())
                    ->label(__('app.doc_series_pattern.list.doc_type')),
                TextColumn::make('name')
                    ->label(__('app.doc_series_pattern.list.name')),
                TextColumn::make('pattern')
                    ->label(__('app.doc_series_pattern.list.pattern')),
                TextColumn::make('switch')
                    ->formatStateUsing(fn($state) => $state->label())
                    ->label(__('app.doc_series_pattern.list.switch')),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->label(__('app.doc_series_pattern.list.is_active')),
                Tables\Columns\IconColumn::make('is_default')
                    ->boolean()
                    ->label(__('app.doc_series_pattern.list.is_default')),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('doc_type')
                    ->label(__('app.doc_series_pattern.list.doc_type'))
                    ->options(
                        DocumentSeriesRepository::getSeriesForTenant(tenant())
                            ->filter(fn(DocumentSeriesPattern $dsp) => $dsp->is_blocked === false)
                            ->map(
                                fn(DocumentSeriesPattern $dsp) => $dsp->setAttribute('label', $dsp->doc_type->label())
                            )
                        ->pluck('label', 'doc_type.value')
                    ),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label(__('app.doc_series_pattern.list.is_active')),
                Tables\Filters\TernaryFilter::make('is_default')
                    ->label(__('app.doc_series_pattern.list.is_default')),
            ])
            ->modifyQueryUsing(
                fn(Builder $query): Builder => $query->where('is_blocked', false)
            )
            ->filtersLayout(Tables\Enums\FiltersLayout::AboveContent)
            ->actions(self::getRowActions())
            ->recordUrl(false)
            ->bulkActions([]);
    }


    public static function getRowActions(): array
    {
        $editAction = Tables\Actions\EditAction::make()
            ->hiddenLabel()
            ->disabled(fn(DocumentSeriesPattern $record): bool => !$record->isEnabledToEdit())
            ->color(
                fn(DocumentSeriesPattern $record): string => $record->isEnabledToEdit() ? 'success' : 'gray'
            );

        $makeDefaultAction = Tables\Actions\Action::make('make_default')
            ->label(__('app.doc_series_pattern.list.make_default'))
            ->color('warning')
            ->requiresConfirmation()
            ->modalHeading('Ustawić wzór jako domyślny?')
            ->icon('heroicon-o-document-check')
            ->disabled(
                fn(
                    DocumentSeriesPattern $record
                ): bool => $record->is_default || $record->is_active === false
            )
            ->action(
                function (DocumentSeriesPattern $record): bool {
                    DocumentSeriesRepository::setDefaultForDocType($record->id, $record->doc_type->value);
                    return true;
                }
            );

        $toggleStateAction = Tables\Actions\Action::make('toggle_state')
            ->label(
                fn(DocumentSeriesPattern $record): string => $record->is_active ?
                    __('app.doc_series_pattern.list.toggle_state_deactivate') :
                    __('app.doc_series_pattern.list.toggle_state_activate')
            )
            ->requiresConfirmation()
            ->modalHeading(
                fn(DocumentSeriesPattern $record): string => match ($record->is_active) {
                    true => __('app.doc_series_pattern.list.toggle_state_heading', [
                        'state' => __('app.doc_series_pattern.list.inactive')
                    ]),
                    default => __('app.doc_series_pattern.list.toggle_state_heading', [
                        'state' => __('app.doc_series_pattern.list.active')
                    ])
                }
            )
            ->icon('heroicon-o-document-check')
            ->disabled(
                fn(
                    DocumentSeriesPattern $record
                ): bool => $record->is_default && $record->is_active === true
            )
            ->action(
                function (DocumentSeriesPattern $record, Tables\Actions\Action $action): bool {
                    if ($record->warehouseDocuments()->notAccepted()->count() > 0) {
                        Notification::make()
                            ->warning()
                            ->title(__('app.doc_series_pattern.list.notify_change_state_title'))
                            ->body(__('app.doc_series_pattern.list.notify_change_state_body', []))
                            ->send();
                        $action->halt();
                    }
                    DocumentSeriesRepository::togglePatternState($record);
                    return true;
                }
            );

        return [
            $editAction,
            Tables\Actions\ActionGroup::make([
                $makeDefaultAction,
                $toggleStateAction
            ])
        ];
    }


    public static function getRelations(): array
    {
        return [
            //
        ];
    }


    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDocumentSeriesPatterns::route('/'),
            'create' => Pages\CreateDocumentSeriesPattern::route('/create'),
            'edit' => Pages\EditDocumentSeriesPattern::route('/{record}/edit'),
        ];
    }
}
